import subprocess
import threading
import time
import os
import signal
import requests

class RTSPStreamProcessor:
    def __init__(self, stream_id, rtsp_url, output_dir="recordings", api_endpoint="http://robot.nick.ge:1024/infer"):
        self.stream_id = stream_id
        self.rtsp_url = rtsp_url
        self.output_dir = output_dir
        self.api_endpoint = api_endpoint # <-- New: API endpoint for upload
        self.process = None # This will refer to the current FFmpeg sub-process
        self.thread = None
        self._is_active = False # Indicates if the Python thread for this stream is running its loop
        self._stop_event = threading.Event() # Used to signal the thread to stop

        # AI-triggered recording state
        self._ai_recording_active = False
        self._ai_recording_timer = None
        self._last_high_probability_time = None
        self._recording_lock = threading.Lock()

        os.makedirs(self.output_dir, exist_ok=True)

    def _upload_recording(self, filepath):
        """
        Uploads a recorded video segment to the specified API endpoint.
        Deletes the file after successful upload.
        """
        print(f"[Stream {self.stream_id}] Attempting to upload {os.path.basename(filepath)} to API...")
        try:
            with open(filepath, 'rb') as f:
                # The 'files' dictionary creates a multipart/form-data request
                # 'file' is the field name on the API side, (filename, file_object, content_type)
                files = {'file': (os.path.basename(filepath), f, 'video/mp4')}
                response = requests.post(self.api_endpoint, files=files)
            
            if response.status_code == 200:
                print(f"[Stream {self.stream_id}] Successfully uploaded {os.path.basename(filepath)}. Response: {response.text}")
                # Delete the file after successful upload
                try:
                    f.close()
                    os.remove(filepath)
                    print(f"[Stream {self.stream_id}] Deleted local file {os.path.basename(filepath)}.")
                except OSError as e:
                    print(f"[Stream {self.stream_id}] Error deleting file {os.path.basename(filepath)}: {e}")
                return True
            else:
                print(f"[Stream {self.stream_id}] API upload failed for {os.path.basename(filepath)}. Status: {response.status_code}, Response: {response.text}")
                # Keep the file if upload failed for inspection/manual retry
                return False
        except requests.exceptions.ConnectionError as e:
            print(f"[Stream {self.stream_id}] Network error during upload for {os.path.basename(filepath)}: {e}")
            return False
        except Exception as e:
            print(f"[Stream {self.stream_id}] An unexpected error occurred during upload for {os.path.basename(filepath)}: {e}")
            return False


    def _analyse_ai_result(self, result):
        """
        Analyzes AI result and triggers recording if any object has probability > 0.6
        Expected result format: {results: [probability: 0.54690, timestamp: 0.0.0.0]}
        """
        try:
            if not result or 'results' not in result:
                print(f"[Stream {self.stream_id}] Invalid AI result format: {result}")
                return

            results_array = result['results']
            if not isinstance(results_array, list):
                print(f"[Stream {self.stream_id}] Results is not an array: {results_array}")
                return

            # Check if any object has probability > 0.6
            high_probability_detected = False
            for obj in results_array:
                if isinstance(obj, dict) and 'probability' in obj:
                    probability = float(obj.get('probability', 0))
                    if probability > 0.6:
                        high_probability_detected = True
                        print(f"[Stream {self.stream_id}] High probability detected: {probability}")
                        break

            with self._recording_lock:
                if high_probability_detected:
                    self._last_high_probability_time = time.time()
                    if not self._ai_recording_active:
                        self._start_recording_after_analisys()
                    else:
                        # Cancel any pending stop timer since we detected another high probability
                        if self._ai_recording_timer:
                            self._ai_recording_timer.cancel()
                            self._ai_recording_timer = None
                            print(f"[Stream {self.stream_id}] Cancelled stop timer due to continued high probability detection")
                else:
                    # No high probability detected, start 3-second countdown if recording is active
                    if self._ai_recording_active and not self._ai_recording_timer:
                        print(f"[Stream {self.stream_id}] No high probability detected, starting 3-second stop countdown")
                        self._ai_recording_timer = threading.Timer(3.0, self._stop_recording_after_analisys)
                        self._ai_recording_timer.start()

        except Exception as e:
            print(f"[Stream {self.stream_id}] Error analyzing AI result: {e}")

    def _start_recording_after_analisys(self):
        """
        Starts AI-triggered recording when probability > 0.6 is detected
        """
        try:
            with self._recording_lock:
                if self._ai_recording_active:
                    print(f"[Stream {self.stream_id}] AI recording already active")
                    return

                self._ai_recording_active = True
                print(f"[Stream {self.stream_id}] Starting AI-triggered recording")

                # Cancel any existing stop timer
                if self._ai_recording_timer:
                    self._ai_recording_timer.cancel()
                    self._ai_recording_timer = None

                # Here you can add specific recording logic for AI-triggered events
                # For now, we'll just log that recording started
                # You might want to start a separate FFmpeg process for event recording
                # or modify the existing recording behavior

        except Exception as e:
            print(f"[Stream {self.stream_id}] Error starting AI recording: {e}")

    def _stop_recording_after_analisys(self):
        """
        Stops AI-triggered recording 3 seconds after no high probability detection
        """
        try:
            with self._recording_lock:
                if not self._ai_recording_active:
                    print(f"[Stream {self.stream_id}] AI recording not active, nothing to stop")
                    return

                # Check if we should actually stop (no high probability in last 3 seconds)
                current_time = time.time()
                if (self._last_high_probability_time and
                    current_time - self._last_high_probability_time < 3.0):
                    print(f"[Stream {self.stream_id}] High probability detected recently, not stopping recording")
                    return

                self._ai_recording_active = False
                self._ai_recording_timer = None
                print(f"[Stream {self.stream_id}] Stopping AI-triggered recording")

                # Here you can add specific logic to stop the AI-triggered recording
                # This might involve stopping a separate FFmpeg process or changing recording mode

        except Exception as e:
            print(f"[Stream {self.stream_id}] Error stopping AI recording: {e}")


    def _run_ffmpeg(self):
        """
        Internal method to continuously record 2-second segments and upload them.
        """
        # This log file will capture FFmpeg's own stderr output across all segments
        ffmpeg_log_path = os.path.join(self.output_dir, f"ffmpeg_core_debug_{self.stream_id}_{int(time.time())}.log")
        
        print(f"[Stream {self.stream_id}] FFmpeg process debug will be logged to: {ffmpeg_log_path}")

        # This loop will run continuously, spawning FFmpeg processes for each segment
        while not self._stop_event.is_set():
            # Generate a unique filename for each 2-second segment
            segment_filename = os.path.join(self.output_dir, f"stream_{self.stream_id}_{int(time.time())}.mp4")
            
            command = [
                'ffmpeg',
                '-hide_banner', # Hide FFmpeg banner
                '-loglevel', 'error', # Only show errors from FFmpeg in the log file
                '-rtsp_transport', 'tcp', # Explicitly use TCP transport
                '-i', self.rtsp_url,  # Input RTSP stream
                '-t', '2', # <-- CRITICAL: Record for exactly 2 seconds
                '-c:v', 'libx264',      # Re-encode video to H.264
                '-preset', 'ultrafast', # Use ultrafast for minimal CPU overhead on short segments
                '-crf', '30',           # High compression (lower quality), but smaller files and faster encoding
                '-c:a', 'aac',          # Re-encode audio to AAC
                '-b:a', '64k',          # Lower audio bitrate
                '-map', '0',            # Map all streams from input to output
                '-f', 'mp4',            # Output format
                '-movflags', 'frag_keyframe+empty_moov', # Optimize MP4 for continuous writing
                segment_filename        # Output file for this segment
            ]

            print(f"[Stream {self.stream_id}] Starting new 2-sec segment recording to: {segment_filename}")

            self.process = None # Clear previous process reference
            try:
                # Open the log file in append mode ('a') to capture output from all segments
                with open(ffmpeg_log_path, 'a') as log_file:
                    self.process = subprocess.Popen(
                        command,
                        stdout=subprocess.DEVNULL, # Send stdout to null device
                        stderr=log_file,          # Redirect stderr (FFmpeg's verbose output) to our log file
                        bufsize=0                 # Unbuffered
                    )
                
                # Mark this stream's Python thread as active while FFmpeg is running
                self._is_active = True 
                
                # Wait for the current FFmpeg process (2-sec segment) to complete
                return_code = self.process.wait() 
                self._is_active = False # Mark as inactive once this segment is done

                if self._stop_event.is_set():
                    print(f"[Stream {self.stream_id}] Stop signal received during segment processing. Exiting loop.")
                    break # Exit loop immediately if stop was signaled

                if return_code == 0:
                    print(f"[Stream {self.stream_id}] Segment recorded successfully: {segment_filename}")
                    self._upload_recording(segment_filename) # Call the upload function
                else:
                    print(f"[Stream {self.stream_id}] FFmpeg failed to record segment (Return Code: {return_code}). Check {ffmpeg_log_path}")
                    # If FFmpeg failed, we might want to pause longer before retrying to prevent rapid-fire failures
                    time.sleep(5) 
            
            except FileNotFoundError:
                print(f"[Stream {self.stream_id}] Error: FFmpeg not found. Make sure it's installed and in your PATH.")
                self._stop_event.set() # Critical error, signal the main thread to stop this stream
            except Exception as e:
                print(f"[Stream {self.stream_id}] An unexpected error occurred during segment recording: {e}")
                time.sleep(5) # Pause on unexpected errors

            # Small delay between segments to prevent CPU thrashing and allow resources to free up
            # Adjust this value based on your system's performance and API response time
            time.sleep(0.5) # Wait half a second before starting the next 2-sec segment
        
        print(f"[Stream {self.stream_id}] Stopping segment recording loop.")
        # Ensure any lingering FFmpeg process is terminated if the loop was stopped externally
        if self.process and self.process.poll() is None:
            print(f"[Stream {self.stream_id}] Force terminating lingering FFmpeg process for segment.")
            self.process.terminate()
            self.process.wait(timeout=2)
            if self.process.poll() is None:
                self.process.kill()
                self.process.wait()
        self.process = None # Clear reference




    def start(self):
        """Starts the FFmpeg recording thread for this stream."""
        # Only start if the thread is not already running or has explicitly stopped
        if not (self.thread and self.thread.is_alive()) or self._stop_event.is_set():
            self._stop_event.clear() # Clear the stop signal before starting
            self.thread = threading.Thread(target=self._run_ffmpeg, daemon=True)
            self.thread.start()
            print(f"[Stream {self.stream_id}] Attempting to start stream thread for: {self.rtsp_url}")
        else:
            print(f"[Stream {self.stream_id}] Stream thread for {self.stream_id} is already running.")

    def stop(self):
        """Sends a stop signal to the FFmpeg recording thread and waits for it to terminate."""
        # Clean up AI recording timer
        with self._recording_lock:
            if self._ai_recording_timer:
                self._ai_recording_timer.cancel()
                self._ai_recording_timer = None
            self._ai_recording_active = False

        if self.thread and self.thread.is_alive():
            print(f"[Stream {self.stream_id}] Signaling FFmpeg process to stop...")
            self._stop_event.set() # Signal the _run_ffmpeg loop to exit
            # Wait for the thread to finish its current segment and exit the loop
            self.thread.join(timeout=5)
            if self.thread.is_alive():
                print(f"[Stream {self.stream_id}] Thread did not terminate gracefully within timeout.")
            print(f"[Stream {self.stream_id}] Stream stop signal sent.")
        else:
            print(f"[Stream {self.stream_id}] FFmpeg process not active or already stopped.")

    def is_running(self):
        """Checks if the Python thread managing the FFmpeg segmenting loop is still active."""
        return self.thread and self.thread.is_alive() and not self._stop_event.is_set()

    def is_ai_recording_active(self):
        """Checks if AI-triggered recording is currently active."""
        with self._recording_lock:
            return self._ai_recording_active

# --- Your main application logic (app.py) will remain similar ---
# Example usage in app.py:
# from your_module import RTSPStreamProcessor # Assuming the class is in a module

# stream_config = {
#     'cam_1': {
#         'url': 'rtsp://rtspstream:<EMAIL>/people',
#         'api_endpoint': 'http://localhost:5000/upload_video' # <--- IMPORTANT: Replace with your actual API endpoint
#     }
# }

# active_streams = {}

# # In your start_streams function:
# for stream_id, config in stream_config.items():
#     processor = RTSPStreamProcessor(
#         stream_id=stream_id,
#         rtsp_url=config['url'],
#         output_dir="recordings",
#         api_endpoint=config['api_endpoint'] # Pass the API endpoint
#     )
#     processor.start()
#     active_streams[stream_id] = processor

# # In your monitoring loop:
#     # Check all streams
#     all_stopped = True
#     for stream_id, processor in list(active_streams.items()): # Iterate over a copy
#         if processor.is_running():
#             all_stopped = False
#         else:
#             print(f"[Stream {stream_id}] FFmpeg segmenting loop has stopped.")
#             # Optionally remove from active_streams if it truly should not restart
#             # del active_streams[stream_id] 
#             # If you want it to restart immediately if it stopped unexpectedly, 
#             # you'd re-call processor.start() here.
#     if all_stopped:
#         print("No active FFmpeg streams. All processes have stopped. Exiting.")
#         break


# import subprocess
# import threading
# import time
# import os
# import signal
# import requests
# import json
# from collections import deque
# from datetime import datetime, timedelta

# # --- IMPORTANT DEBUGGING CHANGE: Configure logging for more verbosity ---
# import logging
# # Set logging level to DEBUG to see all messages, including FFmpeg's detailed output
# logging.basicConfig(level=logging.DEBUG, 
#                     format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s')

# class RTSPStreamProcessor:
#     def __init__(self, stream_id, rtsp_url, output_dir="recordings", api_endpoint="http://your.api.com/upload", ai_api_url="http://robot.nick.ge:1024/", clip_duration_sec=2):
#         self.stream_id = stream_id
#         self.rtsp_url = rtsp_url
#         self.output_dir = output_dir
#         os.makedirs(self.output_dir, exist_ok=True)
#         self.api_endpoint = api_endpoint
#         self.clip_duration_sec = clip_duration_sec
#         self.running = threading.Event()
#         self.running.set()
#         self.process = None # For 2-second segments
        
#         self.ai_api_url = ai_api_url

#         self.pre_buffer_clips = deque(maxlen=int(5 / self.clip_duration_sec) + 2)
        
#         self._triggered_recording_active = False
#         self._current_event_start_abs_time = None
#         self._last_drop_abs_time = None
        
#         self._triggered_recording_process = None # For the live event recording
#         self._current_triggered_event_filepath = None
        
#         self._ai_analysis_lock = threading.Lock()
#         self._triggered_rec_control_lock = threading.Lock()

#     def _parse_relative_timestamp_to_timedelta(self, timestamp_str):
#         try:
#             parts = timestamp_str.split(':')
#             minutes = int(parts[0])
#             seconds_ms = float(parts[1])
#             seconds = int(seconds_ms)
#             milliseconds = int((seconds_ms - seconds) * 1000)
#             return timedelta(minutes=minutes, seconds=seconds, milliseconds=milliseconds)
#         except ValueError as e:
#             logging.error(f"[Stream {self.stream_id}] Error parsing timestamp '{timestamp_str}': {e}")
#             return timedelta(0)

#     def _add_to_pre_buffer(self, filepath, clip_start_abs_time, clip_end_abs_time):
#         with self._ai_analysis_lock:
#             if not any(clip['filepath'] == filepath for clip in self.pre_buffer_clips):
#                 self.pre_buffer_clips.append({
#                     'filepath': filepath,
#                     'start_abs_time': clip_start_abs_time,
#                     'end_abs_time': clip_end_abs_time
#                 })

#     def _process_ai_results(self, analysis_array, clip_absolute_start_time):
#         with self._ai_analysis_lock:
#             if not analysis_array:
#                 logging.info(f"[Stream {self.stream_id}] No AI analysis data received.")
#                 return

#             current_clip_had_event_above_threshold = False 
            
#             for frame_analysis in analysis_array:
#                 probability = frame_analysis['probability']
#                 relative_timestamp_td = self._parse_relative_timestamp_to_timedelta(frame_analysis['timestamp'])
#                 frame_absolute_time = clip_absolute_start_time + relative_timestamp_td

#                 logging.debug(f"[Stream {self.stream_id}] Frame at abs {frame_absolute_time.strftime('%H:%M:%S.%f')[:-3]} Probability: {probability:.4f}")

#                 if probability > 0.6:
#                     current_clip_had_event_above_threshold = True
#                     if not self._triggered_recording_active:
#                         self._current_event_start_abs_time = frame_absolute_time
#                         logging.info(f"[Stream {self.stream_id}] Probability crossed 0.6 at relative {frame_analysis['timestamp']} (abs: {frame_absolute_time.strftime('%H:%M:%S.%f')[:-3]}). Triggering recording.")
#                         self._start_triggered_recording()
#                         self._last_drop_abs_time = None
#                     else:
#                         self._last_drop_abs_time = None # Event is still active, reset any pending stop timer

#                 if probability < 0.6 and self._triggered_recording_active:
#                     if self._last_drop_abs_time is None:
#                         self._last_drop_abs_time = frame_absolute_time
#                         logging.info(f"[Stream {self.stream_id}] Probability dropped below 0.6 at relative {frame_analysis['timestamp']} (abs: {frame_absolute_time.strftime('%H:%M:%S.%f')[:-3]}). Starting 5-second post-event timer.")

#             if self._triggered_recording_active and self._last_drop_abs_time:
#                 time_since_last_drop = datetime.now() - self._last_drop_abs_time
#                 logging.debug(f"[Stream {self.stream_id}] Time since last drop: {time_since_last_drop.total_seconds():.2f}s")
#                 if time_since_last_drop >= timedelta(seconds=5):
#                     logging.info(f"[Stream {self.stream_id}] 5 seconds passed since probability dropped below 0.6. Stopping triggered recording.")
#                     self._stop_triggered_recording()

#     def _start_triggered_recording(self):
#         logging.info(f"[Stream {self.stream_id}] Attempting to start triggered recording.")
#         with self._triggered_rec_control_lock:
#             if self._triggered_recording_active:
#                 logging.info(f"[Stream {self.stream_id}] Triggered recording already active. Skipping start.")
#                 return

#             if not self._current_event_start_abs_time:
#                 logging.error(f"[Stream {self.stream_id}] Cannot start triggered recording: _current_event_start_abs_time is not set.")
#                 return # Should not happen if _process_ai_results logic is correct

#             self._triggered_recording_active = True
            
#             # Generate a unique filename for the event clip based on its absolute start time
#             event_timestamp_for_filename = self._current_event_start_abs_time.strftime("%Y%m%d_%H%M%S_%f")[:-3]
#             self._current_triggered_event_filepath = os.path.join(
#                 self.output_dir,
#                 f"event_{self.stream_id}_{event_timestamp_for_filename}.mp4"
#             )

#             command = [
#                 'ffmpeg',
#                 '-hide_banner', '-loglevel', 'info', # Changed to 'info' to see FFmpeg's startup info
#                 '-rtsp_transport', 'tcp',
#                 '-i', self.rtsp_url,
#                 '-c:v', 'libx264', '-preset', 'veryfast', '-crf', '25',
#                 '-c:a', 'aac', '-b:a', '128k',
#                 '-map', '0', # Map all streams (video, audio)
#                 '-f', 'mp4',
#                 '-movflags', 'empty_moov+default_base_moof', # Important for live writing and later concat
#                 '-y', # Overwrite output file if it exists (shouldn't with unique filename)
#                 self._current_triggered_event_filepath
#             ]

#             logging.info(f"[Stream {self.stream_id}] FFmpeg command for live event recording: {' '.join(command)}")
            
#             # --- IMPORTANT DEBUGGING CHANGE ---
#             # Now capture stdout and stderr to a pipe so we can log them.
#             # This is crucial for seeing FFmpeg's specific error messages.
#             self._triggered_recording_process = subprocess.Popen(
#                 command,
#                 stdout=subprocess.PIPE, # Capture stdout
#                 stderr=subprocess.PIPE, # Capture stderr
#                 text=True # Decode stdout/stderr as text for easier logging
#             )

#             # --- NEW: Start a dedicated thread to read and log FFmpeg's stderr ---
#             def log_ffmpeg_output(process, stream_id, stream_type="stderr"):
#                 for line in getattr(process, stream_type):
#                     # Use logging.ERROR for stderr, logging.INFO for stdout (if you capture it too)
#                     logging.error(f"[FFmpeg-Live-{stream_id}-{stream_type.upper()}] {line.strip()}")
            
#             threading.Thread(target=log_ffmpeg_output, args=(self._triggered_recording_process, self.stream_id, "stderr"), daemon=True).start()
#             # If you want to see stdout as well (less critical for errors usually):
#             # threading.Thread(target=log_ffmpeg_output, args=(self._triggered_recording_process, self.stream_id, "stdout"), daemon=True).start()
#             # --- END NEW ---

#             logging.info(f"[Stream {self.stream_id}] Live FFmpeg process for triggered event started (PID: {self._triggered_recording_process.pid}) to: {self._current_triggered_event_filepath}")
            
#             # Immediate check if the file exists (FFmpeg typically creates it quickly)
#             if os.path.exists(self._current_triggered_event_filepath):
#                 logging.info(f"[Stream {self.stream_id}] Output file '{self._current_triggered_event_filepath}' created on disk.")
#             else:
#                 logging.warning(f"[Stream {self.stream_id}] Output file '{self._current_triggered_event_filepath}' NOT immediately created on disk. Check FFmpeg errors.")

#     def _stop_triggered_recording(self):
#         logging.info(f"[Stream {self.stream_id}] Attempting to stop triggered recording.")
#         with self._triggered_rec_control_lock:
#             if not self._triggered_recording_active:
#                 logging.info(f"[Stream {self.stream_id}] Triggered recording not active. Skipping stop.")
#                 return

#             logging.info(f"[Stream {self.stream_id}] Stopping live FFmpeg for triggered event.")
#             if self._triggered_recording_process and self._triggered_recording_process.poll() is None:
#                 # Send SIGINT to gracefully terminate FFmpeg
#                 self._triggered_recording_process.terminate()
#                 try:
#                     self._triggered_recording_process.wait(timeout=5) # Give it some time to shut down
#                 except subprocess.TimeoutExpired:
#                     # If it doesn't terminate, kill it forcefully
#                     self._triggered_recording_process.kill()
#                     logging.error(f"[Stream {self.stream_id}] Forcefully killed FFmpeg process.")
            
#             # Ensure we store the necessary paths/times before resetting the state
#             event_start_time_for_concat = self._current_event_start_abs_time
#             live_recorded_filepath_for_concat = self._current_triggered_event_filepath

#             # Reset state variables
#             self._triggered_recording_active = False
#             self._triggered_recording_process = None
#             self._current_event_start_abs_time = None
#             self._last_drop_abs_time = None

#             # --- Concatenation Logic ---
#             final_clip_list_files = []
            
#             if not event_start_time_for_concat: 
#                 logging.error(f"[Stream {self.stream_id}] Error: No event start time to base concat on. Aborting concat.")
#                 return

#             event_lookback_start_time = event_start_time_for_concat - timedelta(seconds=5)
#             logging.info(f"[Stream {self.stream_id}] Event lookback start time for concat: {event_lookback_start_time.strftime('%H:%M:%S.%f')[:-3]}")

#             # Collect relevant clips from the pre-buffer
#             for clip_info in list(self.pre_buffer_clips): # Iterate on a copy to avoid issues if deque changes
#                 # Clip is relevant if its end time is after the desired lookback start
#                 # AND its start time is before the absolute event trigger time (to capture the segment where event starts)
#                 if clip_info['end_abs_time'] > event_lookback_start_time and \
#                    clip_info['start_abs_time'] < event_start_time_for_concat + timedelta(seconds=self.clip_duration_sec): 
#                     final_clip_list_files.append(clip_info['filepath'])
#                     logging.debug(f"[Stream {self.stream_id}] Adding buffered clip to concat: {clip_info['filepath']} (start: {clip_info['start_abs_time'].strftime('%H:%M:%S.%f')[:-3]})")

#             # Add the main live recorded part (if it exists and is not empty)
#             if live_recorded_filepath_for_concat and \
#                os.path.exists(live_recorded_filepath_for_concat) and \
#                os.path.getsize(live_recorded_filepath_for_concat) > 0:
#                 final_clip_list_files.append(live_recorded_filepath_for_concat)
#                 logging.info(f"[Stream {self.stream_id}] Adding live recorded segment to concat: {live_recorded_filepath_for_concat}")
#             else:
#                 logging.warning(f"[Stream {self.stream_id}] Live triggered recording file not found or empty: {live_recorded_filepath_for_concat}")

#             if len(final_clip_list_files) > 0:
#                 concat_list_filename = os.path.join(self.output_dir, f"concat_list_{self.stream_id}_{int(time.time())}.txt")
#                 final_output_filename = os.path.join(self.output_dir, f"final_event_{self.stream_id}_{int(time.time())}.mp4")

#                 with open(concat_list_filename, 'w') as f:
#                     for fp in final_clip_list_files:
#                         f.write(f"file '{fp.replace(os.sep, '/')}'\n")

#                 logging.info(f"[Stream {self.stream_id}] Concatenating {len(final_clip_list_files)} segments into {final_output_filename}...")
                
#                 concat_command = [
#                     'ffmpeg',
#                     '-hide_banner', '-loglevel', 'error', # Only show errors for concat
#                     '-f', 'concat',
#                     '-safe', '0', # Allows absolute paths in the concat list
#                     '-i', concat_list_filename,
#                     '-c', 'copy', # Copy streams, don't re-encode (fast and lossless)
#                     '-map', '0:v?', '-map', '0:a?', # Map video and audio streams if they exist
#                     final_output_filename
#                 ]

#                 try:
#                     subprocess.run(concat_command, check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
#                     logging.info(f"[Stream {self.stream_id}] Final event clip created: {final_output_filename}")
#                     # You can now upload final_output_filename to your API if needed
#                     # self._upload_final_event_clip(final_output_filename) 
                    
#                 except subprocess.CalledProcessError as e:
#                     logging.error(f"[Stream {self.stream_id}] Error concatenating clips for event: {e}")
#                 finally:
#                     if os.path.exists(concat_list_filename):
#                         os.remove(concat_list_filename)
#             else:
#                 logging.warning(f"[Stream {self.stream_id}] No clips found to concatenate for event. Final event video not created.")
            
#             # Clean up the specific live recorded event file
#             if live_recorded_filepath_for_concat and os.path.exists(live_recorded_filepath_for_concat):
#                 try:
#                     os.remove(live_recorded_filepath_for_concat)
#                     logging.info(f"[Stream {self.stream_id}] Cleaned up temporary live event file: {live_recorded_filepath_for_concat}")
#                 except OSError as e:
#                     logging.error(f"[Stream {self.stream_id}] Error cleaning up temporary live event file: {e}")
#             self._current_triggered_event_filepath = None # Reset for next event

#     def _upload_recording(self, filepath, clip_start_abs_time, clip_end_abs_time):
#         file_basename = os.path.basename(filepath)
#         logging.info(f"[Stream {self.stream_id}] Uploading {file_basename} to AI for analysis...")

#         ai_response_data = None
#         try:
#             with open(filepath, 'rb') as f:
#                 files = {'file': (file_basename, f, 'video/mp4')}
#                 response = requests.post(self.ai_api_url, files=files, timeout=30)
#                 response.raise_for_status() # Raises HTTPError for bad responses (4xx or 5xx)
#                 ai_response_data = response.json()
#                 logging.info(f"[Stream {self.stream_id}] AI analysis for {file_basename} received successfully.")
#                 logging.debug(f"[Stream {self.stream_id}] Full AI response: {ai_response_data}") # Log full data as DEBUG

#                 self._process_ai_results(ai_response_data, clip_start_abs_time)

#         except requests.exceptions.RequestException as e:
#             logging.error(f"[Stream {self.stream_id}] Error uploading or analyzing {file_basename} (Request Error): {e}")
#             if hasattr(e, 'response') and e.response is not None:
#                 logging.error(f"[Stream {self.stream_id}] AI API Response Content: {e.response.text}")
#         except json.JSONDecodeError:
#             logging.error(f"[Stream {self.stream_id}] Failed to decode JSON from AI response for {file_basename}. Raw Response: {response.text}")
#         except Exception as e:
#             logging.exception(f"[Stream {self.stream_id}] An unexpected error occurred during upload/analysis for {file_basename}.")

#         # Add clip to buffer AFTER AI analysis has been attempted, so it's available for pre-buffering.
#         self._add_to_pre_buffer(filepath, clip_start_abs_time, clip_end_abs_time)

#         with self._ai_analysis_lock:
#             is_needed_for_pre_buffer = False
#             # Check if this clip's end time is recent enough to be in the pre-buffer window
#             # Add a small buffer margin to ensure it's kept long enough
#             if datetime.now() - clip_end_abs_time < timedelta(seconds=self.pre_buffer_clips.maxlen * self.clip_duration_sec + 2): 
#                  is_needed_for_pre_buffer = True

#             # Only delete if NOT currently involved in an active triggered recording AND NOT needed for pre-buffer
#             if not self._triggered_recording_active and not is_needed_for_pre_buffer:
#                 try:
#                     os.remove(filepath)
#                     logging.info(f"[Stream {self.stream_id}] Deleted local temporary file {file_basename}.")
#                 except OSError as e:
#                     logging.error(f"[Stream {self.stream_id}] Error deleting file {file_basename}: {e}")
#             elif self._triggered_recording_active:
#                 logging.info(f"[Stream {self.stream_id}] Keeping {file_basename} as a triggered recording is active.")
#             else:
#                 logging.info(f"[Stream {self.stream_id}] Keeping {file_basename} as it's still within the pre-buffer window.")

#         return True

#     def _run_ffmpeg(self):
#         segment_count = 0
#         while self.running.is_set():
#             timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S")
#             segment_filename = os.path.join(self.output_dir, f"{self.stream_id}_{timestamp_str}_{segment_count}.mp4")

#             clip_start_abs_time = datetime.now() 

#             # command = [
#             #     'ffmpeg',
#             #     '-hide_banner', '-loglevel', 'error', # Only show errors for 2-sec segment process
#             #     '-rtsp_transport', 'tcp',
#             #     '-i', self.rtsp_url,
#             #     '-c:v', 'libx264', '-preset', 'ultrafast', '-crf', '30',
#             #     '-c:a', 'aac', '-b:a', '128k',
#             #     '-map', '0',
#             #     '-f', 'mp4',
#             #     '-movflags', 'empty_moov+default_base_moof',
#             #     '-t', str(self.clip_duration_sec),
#             #     '-y',
#             #     segment_filename
#             # ]
#             command = [
#                 'ffmpeg',
#                 '-hide_banner', # Hide FFmpeg banner
#                 '-loglevel', 'error', # Only show errors from FFmpeg in the log file
#                 '-rtsp_transport', 'tcp', # Explicitly use TCP transport
#                 '-i', self.rtsp_url,  # Input RTSP stream
#                 '-t', '2', # <-- CRITICAL: Record for exactly 2 seconds
#                 '-c:v', 'libx264',      # Re-encode video to H.264
#                 '-preset', 'ultrafast', # Use ultrafast for minimal CPU overhead on short segments
#                 '-crf', '30',           # High compression (lower quality), but smaller files and faster encoding
#                 '-c:a', 'aac',          # Re-encode audio to AAC
#                 '-b:a', '64k',          # Lower audio bitrate
#                 '-map', '0',            # Map all streams from input to output
#                 '-f', 'mp4',            # Output format
#                 '-movflags', 'frag_keyframe+empty_moov', # Optimize MP4 for continuous writing
#                 segment_filename        # Output file for this segment
#             ]

#             logging.info(f"[Stream {self.stream_id}] Recording segment {segment_count} to {segment_filename}...")
            
#             # Use PIPE for stdout/stderr to capture potential errors
#             proc = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
#             try:
#                 # Add a timeout to communicate in case FFmpeg hangs
#                 stdout, stderr = proc.communicate(timeout=self.clip_duration_sec + 5) 
#             except subprocess.TimeoutExpired:
#                 #proc.kill()
#                 stdout, stderr = proc.communicate() # Read output after killing
#                 #logging.error(f"[Stream {self.stream_id}] FFmpeg segment process timed out and was killed.")

#                 pass
            
#             return_code = proc.returncode

#             if stdout:
#                 logging.debug(f"[FFmpeg-Segment-{self.stream_id} STDOUT] {stdout.strip()}")
#             if stderr:
#                 logging.error(f"[FFmpeg-Segment-{self.stream_id} STDERR] {stderr.strip()}")

#             clip_end_abs_time = datetime.now()

#             if return_code == 0:
#                 logging.info(f"[Stream {self.stream_id}] Segment recorded successfully: {segment_filename}")
#                 self._upload_recording(segment_filename, clip_start_abs_time, clip_end_abs_time) 
#             else:
#                 logging.error(f"[Stream {self.stream_id}] FFmpeg exited with error code {return_code} for segment {segment_filename}. Check FFmpeg STDERR logs for details.")
#                 # Consider adding a small back-off (e.g., time.sleep(1) or incrementing a retry counter)
#                 # here if many errors occur consecutively to prevent excessive resource usage.
#             segment_count += 1
#             time.sleep(0.1) # Small delay to prevent busy-looping

#     def stop(self):
#         logging.info(f"[Stream {self.stream_id}] Stopping stream processor...")
#         self.running.clear()
        
#         # Stop the 2-second segment FFmpeg process
#         if self.process and self.process.poll() is None:
#             self.process.terminate()
#             try:
#                 self.process.wait(timeout=5)
#             except subprocess.TimeoutExpired:
#                 self.process.kill()
#                 logging.error(f"[Stream {self.stream_id}] Forcefully killed 2-second segment FFmpeg process.")
        
#         # Stop and clean up any active triggered recording
#         with self._triggered_rec_control_lock:
#             if self._triggered_recording_active:
#                 logging.info(f"[Stream {self.stream_id}] Stopping active triggered recording during shutdown.")
#                 self._stop_triggered_recording() # Ensure cleanup of event recording

#         logging.info(f"[Stream {self.stream_id}] Stream processor stopped.")