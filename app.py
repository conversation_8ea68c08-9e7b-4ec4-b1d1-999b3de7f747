from processor import RTSPStreamProcessor
import time


def main():
    # Define your RTSP stream URLs
    rtsp_streams = {
        # Valid test stream (may become inactive over time)
        #"cam_1": 'rtsp://rtspstream:<EMAIL>/people',
        "cam_3": 'rtsp://localhost:8554/shop_lifting',

        # # Another valid stream (for demonstration)
        # "cam_2": 'rtsp://wowzaec2amo.streamlock.net/vod/mp4:BigBuckBunny_115k.mov',
        # # An invalid stream URL for testing failure cases
        # "bad_cam_3": 'rtsp://invalid.stream.url/nonexistent_stream',
        # # Example of a potentially slow/unresponsive stream
        # "slow_cam_4": 'rtsp://invalid.stream.url/another_nonexistent_stream'
    }

    processors = []
    for stream_id, url in rtsp_streams.items():
        processor = RTSPStreamProcessor(stream_id, url)
        processors.append(processor)
        processor.start()

    print("\nAll stream start requests sent. Monitoring active streams. Press Ctrl+C to stop all streams.")


    try:
        while True:
            active_streams = [p for p in processors if p.is_running()]
            if not active_streams:
                print("No active FFmpeg streams. All processes have stopped. Exiting.")
                break # Exit if all streams legitimately stop or fail to start

            print(f"\nCurrently {len(active_streams)} stream(s) active: {[p.stream_id for p in active_streams]}")
            time.sleep(10) # Check status every 10 seconds
            
    except KeyboardInterrupt:
        print("\nCtrl+C detected. Stopping all streams...")
    finally:
        for processor in processors:
            processor.stop()

        #for thread in threads:
#             thread.join(timeout=10) # Wait for threads to finish, with a timeout
#         print("All stream processors stopped.")
        print("All streams stopped. Exiting program.")

if __name__ == "__main__":
    main()



# # In your app.py or wherever you initialize RTSPStreamProcessor
# from processor import RTSPStreamProcessor # Assuming your class is in this file
# import threading
# import time

# # Example usage:
# if __name__ == "__main__":
#     # Ensure your AI API URL is correct. This is where your 2-second clips will be POSTed.
#     AI_API_URL = "http://robot.nick.ge:1024/infer" # Replace with your actual AI API endpoint

#     # Define your RTSP streams
#     streams = [
#         {"id": "camera1", "url": "rtsp://localhost:8000/shop_lifting", "output_dir": "recordings/camera1"},
#         # Add more cameras as needed
#     ]

#     processors = []
#     threads = []

#     for stream_info in streams:
#         processor = RTSPStreamProcessor(
#             stream_id=stream_info["id"],
#             rtsp_url=stream_info["url"],
#             output_dir=stream_info["output_dir"],
#             api_endpoint="http://your.api.com/upload", # Your existing upload API, if any
#             ai_api_url=AI_API_URL, # Pass the AI API URL
#             clip_duration_sec=2 # The duration of clips sent for AI analysis
#         )
#         processors.append(processor)
        
#         thread = threading.Thread(target=processor._run_ffmpeg, daemon=True) # Run FFmpeg in a daemon thread
#         threads.append(thread)
#         thread.start()
#         print(f"Started processing for {stream_info['id']}")

#     try:
#         while True:
#             time.sleep(1) # Keep the main thread alive
#     except KeyboardInterrupt:
#         print("Stopping all stream processors...")
#         for processor in processors:
#             processor.stop() # Gracefully stop each processor
#         for thread in threads:
#             thread.join(timeout=10) # Wait for threads to finish, with a timeout
#         print("All stream processors stopped.")